<template>
  <div class="training-page">
    <div class="left-sidebar" v-loading="loading">
      <knowledgeTree
        :selected="chapterId"
        :iswen="true"
        :options="options"
        :showCurrentTaskIndicator="!!routeHasChapterId"
        currentTaskImageUrl="@/assets/img/synchronous/task-badge.png"
        @setChapterId="setChapterId"
      />
      <div class="sidebar-title">
        选择章节
        <span class="title-decoration"></span>
      </div>
    </div>

    <div class="main-content" v-loading="loading2">
      <div class="content-head">
        <div class="head-body">
          <img src="@/assets/img/percision/training/textbook.png" alt="" class="textbook" />
          <div class="head-title">
            当前教材：{{ bookVersionName }}
          </div>
          <div @click="onModify" class="head-switch">切换教材</div>
        </div>

        <img @click="onMark" src="@/assets/img/percision/training/superficiality.png" alt="" class="superficiality" />
        <div class="catalogue">
          <span>{{ chapterPathText }}</span>
          <img src="@/assets/img/percision/training/dsj.png" alt="">
        </div>      
      </div>

      <div class="content-tip">
        <div class="tip-content">
          <img src="@/assets/img/percision/training/mty.png" alt="" class="tip-avatar" />
          <div class="tip-text">本节为老师布置的任务，请在规定时间内完成。<span style="color:#DD2A2A">截止时间：2025/05/15</span></div>
        </div>
        <img src="@/assets/img/percision/training/tip_bg.png" class="tip-bg" alt="" />
      </div>
      <!-- 知识点训练列表 -->
      <div class="knowledge-training-list">
        <div class="lesson-section" v-if="lessonData.length > 0">

          <div class="knowledge-info">
            <div class="knowledge-box">
              <!-- 将知识点每两个分为一课 -->
              <div 
                class="lesson-container" 
                v-for="(_, lessonIndex) in Math.ceil(lessonData.length / 2)" 
                :key="'lesson-' + lessonIndex"
              >
                <div class="lesson-header">
                  <div class="lesson-title">第{{ lessonIndex + 1 }}课</div>
                </div>
                
                <div class="lesson-content">
                  <!-- 遍历当前课的两个知识点 -->
                  <div 
                    class="knowledge-item" 
                    v-for="knowledge in lessonData.slice(lessonIndex * 2, lessonIndex * 2 + 2)" 
                    :key="knowledge.id"
                  >
                    <div class="knowledge-name" @click="showKnowledgeDetail(knowledge)">
                      <span class="knowledge-title">{{ knowledge.name }}</span>
                    </div>
                    <div class="progress-area">
                      <div class="hexagon-group">
                        <!-- 基础训练六边形 -->
                        <div class="hexagon-wrapper">
                          <div class="hexagon-bg" :class="getHexagonBgClass(knowledge.correctRate || 0, 'basic')">
                            <div class="hexagon-content">

                              <img v-if="(knowledge.correctRate || 0) >= 100"
                                   :src="getSmallMedalIcon(1)"
                                   class="medal-crown" />
                              <div class="percentage-text">{{ knowledge.correctRate || 0 }}%</div>
                            </div>
                          </div>
                        </div>

                        <el-icon><CaretRight color="#EAEAEA" /></el-icon>          
                        <!-- 进阶训练六边形 -->
                        <div class="hexagon-wrapper">
                          <div class="hexagon-bg" :class="getHexagonBgClass(knowledge.correctRate || 0, 'advanced')">
                            <div class="hexagon-content">
                              <div class="percentage-text">{{ knowledge.correctRate || 0 }}%</div>
                              <img v-if="(knowledge.correctRate || 0) >= 100"
                                   :src="getSmallMedalIcon(2)"
                                   class="medal-crown" />
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                    <div class="action-area">
                      <div class="action-btn study" @click="toPractice(1, knowledge.id || '')">
                        <img src="@/assets/img/percision/training/play.png" class="action-icon" />
                        去学习
                      </div>
                      <div class="action-btn practice" @click="toPractice(4, knowledge.id || '')">
                        <img src="@/assets/img/percision/training/practice.png" class="action-icon"  />
                        练习记录
                      </div>
                    </div>
                    
                    <!-- 知识点状态指示器 -->
                    <!-- <div class="knowledge-status" :class="getKnowledgeStatusClass(knowledge)">
                      {{ getKnowledgeStatusText(knowledge) }}
                    </div> -->
                  </div>
                </div>
                
                <!-- 挑战按钮 -->
                <div 
                  class="challenge-btn"
                  @click="handleChallengeClick({id: lessonIndex + 1, knowledgePoints: lessonData.slice(lessonIndex * 2, lessonIndex * 2 + 2)})"
                >
                  {{ getChallengeButtonText({knowledgePoints: lessonData.slice(lessonIndex * 2, lessonIndex * 2 + 2)}) }}
                </div>
              </div>
            </div>
          </div>
        </div>
        
        <div v-else class="empty-state">
          <div class="empty-message">暂无知识点数据</div>
        </div>
      </div>
      
      <!-- 知识点详情弹窗 -->
      <el-dialog
        v-model="knowledgeDetailVisible"
        :title="selectedKnowledge?.name || '知识点详情'"
        width="60%"
        :before-close="handleDetailClose"
      >
        <div class="knowledge-detail-content" v-if="selectedKnowledge">
          <div v-html="selectedKnowledge.desc"></div>
        </div>
      </el-dialog>
    </div>
    <div v-if="challengePop" class="elevate-overlay">
      <div class="elevate-ct">
        <div class="close-btn" @click="challengePop = false">
          <img src="@/assets/img/percision/training/hscc.png" alt="">
        </div>
        <div class="top-title">本次检测{{ selectedLessonForChallenge?.knowledgePoints?.length || 0 }}个知识点</div>
        <div class="block-ct">
          <div 
            class="book-list" 
            v-for="(point, index) in selectedLessonForChallenge?.knowledgePoints" 
            :key="point.id"
          >
            <img src="@/assets/img/percision/training/bookzsd.png" alt="">
            <div class="book-name">{{ index + 1 }}.{{ point.name }}</div>
            <div class="book-tl">题量<span class="num">{{ point.total || 3 }}</span></div>
            <div class="book-tl">难度<span class="num">{{ getDifficultyText(point) }}</span></div>
          </div>
          <div class="prompt"> 共 <span>{{ getTotalQuestions() }} </span>道题，要求 <span>{{ getEstimatedTime() }}</span> 分钟内完成</div>
        </div>
        <div class="challenge-fq">向<img src="@/assets/img/percision/training/qingtong.png" alt="">发起挑战吧，正确率≥90%即可过关！</div>
        <div class="book-challenge" @click="onChallenge">开始挑战</div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { useRouter ,useRoute} from 'vue-router'
import knowledgeTree from "@/views/components/knowledgeTree/trainingTree.vue"
import { getBookChapterListApi, getMasteryApi, getBookChapterListsApi, getPointCategoryApi,getChapterListApi,getpointListApi } from "@/api/book"
import { computed, nextTick, onMounted, reactive, ref, watch } from 'vue'
import { useUserStore } from "@/store/modules/user"
import { dataEncrypt, dataDecrypt } from "@/utils/secret"
import { storeToRefs } from 'pinia'
import { CaretRight } from '@element-plus/icons-vue'
const userStore = useUserStore()

const { subjectObj, learnNow } = storeToRefs(userStore)
const learnUsers = JSON.parse(localStorage.learnUsers || "[]") || []
const router = useRouter()
const route = useRoute()
const query = reactive<any>(route.query)
const loading = ref(false)
const loading2 = ref(false)
const pageStatus = ref(true)
const challengePop = ref(false)

const chapterId = ref("")
const routeHasChapterId = ref(false) // 标记是否从路由获取了章节ID
const isTargetChapterId = ref()
const chapterPath = ref<any>([]) // 存储当前章节路径

// 计算属性：格式化章节路径文本
const chapterPathText = computed(() => {
  if (!chapterPath.value || chapterPath.value.length === 0) {
    return "请选择章节"
  }
  
  return chapterPath.value
    .map(chapter => chapter.chapterName || chapter.name)
    .join(' > ')
})

const chapterData = reactive<any>({
  percentage1: null,
  percentage1i: null,
  strong1: false,
  rate1: 1,
  percentage2: null,
  percentage2i: null,
  strong2: false,
  rate2: 2,
  percentage3: null,
  percentage3i: null,
  strong3: false,
  rate3: 3,
  percentage0: null,
  percentage0i: null,
  strong0: false,
})
const options = ref([])

// 课程数据结构
const lessonData = ref([
  {
    id: 1,
    name: '第1课',
    correctRate: 89.4,
    studyStatus: 0,
    status: 5,
    knowledgePoints: [
      {
        id: 'k1',
        name: '知识点名称',
        basicProgress: 89.4,
        advancedProgress: 33,
        isCompleted: false,
        completionLevel: null
      },
      {
        id: 'k2',
        name: '知识点名称知识点名称知识点名称知识点名称知识点名称知识点名称知识点名称知识点名称知识点名称知识点名称知识点名称知识点名称知识点名称知识点名称知识点名称知识点名称知识点名称知识点名称知识点名称知识点名称知识点名称知识点名称',
        basicProgress: 89.4,
        advancedProgress: 33,
        isCompleted: false,
        completionLevel: null
      }
    ]
  },
  {
    id: 2,
    name: '第2课',
    correctRate: 100,
    studyStatus: 1,
    status: 1,
    knowledgePoints: [
      {
        id: 'k3',
        name: '知识点名称',
        basicProgress: 100,
        advancedProgress: 100,
        isCompleted: true,
        completionLevel: 'perfect'
      },
      {
        id: 'k4',
        name: '知识点名称知识点名称知识点名称知识点名称知知识点名称知识点名称知识点名称知识点名称知识点名称识点名称',
        basicProgress: 100,
        advancedProgress: 100,
        isCompleted: true,
        completionLevel: 'perfect'
      }
    ]
  },
  {
    id: 3,
    name: '第3课',
    correctRate: 0,
    studyStatus: 0,
    status: 5,
    knowledgePoints: [
      {
        id: 'k5',
        name: '知识点名称',
        basicProgress: 0,
        advancedProgress: 0,
        isCompleted: false,
        completionLevel: null
      },
      {
        id: 'k6',
        name: '知识点名称知识点名称知识点名称知识点名称知识点名称',
        basicProgress: 0,
        advancedProgress: 0,
        isCompleted: false,
        completionLevel: null
      }
    ]
  }
])
// 处理挑战按钮点击
const handleChallengeClick = (lesson: any) => {
  // 获取当前课程的知识点ID列表
  const pointIds = lesson.knowledgePoints.map((point: any) => point.id);
  
  // 显示挑战弹窗
  challengePop.value = true;
  
  // 设置弹窗数据
  selectedLessonForChallenge.value = {
    id: lesson.id,
    knowledgePoints: lesson.knowledgePoints,
    pointIds: pointIds
  };
}

// 存储当前选中的课程数据，用于挑战
const selectedLessonForChallenge = ref<any>(null);

const onChallenge = () => {
  if (!selectedLessonForChallenge.value) return;
  
  router.push({
    path: '/ai_percision/foundation_report',
    query: {
      data: dataEncrypt({
        sourceId: 24796,
        chapterId: chapterId.value,
        pointId: selectedLessonForChallenge.value.pointIds || [],
        subject: subjectObj.value.id,
        type: 1,
        step: 1,
        contentType: query.contentType,
        isPromote: 0
      }),
      type: 1,
      contentType: query.contentType
    }
  });
}
const getUrl = (item: any) => {
    let url = 'pen'
    if (item > 90) {
        url = 'strong'
    }
    return new URL(`../../../assets/img/percision/${url}.png`, import.meta.url).href //静态资源引入为url，相当于require()
}
// 递归查找章节，并返回包含该章节的完整路径
const findChapterById = (chapters:any, targetId:any, path:any = []) => {
  let deepestPath: any[] | null = null;

  for (const chapter of chapters) {
    // 确保章节有chapterName属性
    if (!chapter.chapterName && chapter.name) {
      chapter.chapterName = chapter.name
    }
    
    // 创建当前路径的副本
    const currentPath:any = [...path, chapter]

    // 如果找到目标章节，记录当前路径
    if (chapter.id === targetId) {
      // 记录找到的路径，但不立即返回
      deepestPath = currentPath;
      // 更新全局的章节路径
      chapterPath.value = deepestPath;
    }

    // 无论是否已找到匹配节点，都继续递归查找子节点
    if (chapter.children && chapter.children.length > 0) {
      const childResult: any[] | null = findChapterById(chapter.children, targetId, currentPath)
      // 如果子节点中找到了结果，优先使用子节点的结果（更深层次）
      if (childResult) {
        // 如果已经有记录但找到了更深的路径，或者还没有记录
        if (!deepestPath || childResult.length > deepestPath.length) {
          deepestPath = childResult;
          // 更新全局的章节路径
          chapterPath.value = deepestPath;
        }
      }
    }
  }

  return deepestPath;
}

onMounted(async () => {
  const chapters = await getBookChapterList()
  console.log(query.contentType, "query.contentType")
  // 从路由参数中获取章节ID
  let targetChapterId = null

  if (query.chapterId) {
    console.log(query.chapterId,"query.chapterIdquery.chapterId")
    targetChapterId = query.chapterId
  } else if (query.data) {
    try {
      // 尝试解密data参数
      const decryptedData = dataDecrypt(query.data)
      if (decryptedData && decryptedData.chapterId) {
        targetChapterId = decryptedData.chapterId
      }
    } catch (error) {
      console.error('Failed to decrypt query data:', error)
    }
  }

  // 如果找到目标章节ID，设置选中状态并获取掌握度
  if (targetChapterId) {
    chapterId.value = targetChapterId
    // 标记章节ID来自路由
    routeHasChapterId.value = true

    // 查找章节路径，可用于展开树形结构
    const foundPath = findChapterById(chapters, targetChapterId)
    console.log("targetChapterId", targetChapterId)

    if (foundPath && foundPath.length > 0) {
      // 获取最后一个节点（目标章节）的名称
      const targetChapter = foundPath[foundPath.length - 1]
      // 设置页面状态（是否为单元测试）
      pageStatus.value = targetChapter.name !== "单元测试"
    }

    // 获取掌握度数据
    getMastery()
  }
})

//获取章节列表
const getBookChapterList = async() => {
  if(query.contentType == 'historyTask'){
    subjectObj.value.bookId = query.bookId
  }
  loading.value = true
  loading2.value = true
  try {
    const res: any = await getChapterListApi({
      bookId:subjectObj.value.bookId,
      hierarchy: 3,
      type: 1,
      chapterIds: query?.resourceIds?query?.chapterId:''
    })
    if(res.code == 200) {
      console.log(res,"打印一i西安 左侧 数据")
      
      // 确保所有节点都有chapterName属性
      const processChapterData = (chapters) => {
        return chapters.map(chapter => {
          // 确保每个节点都有chapterName属性
          const processedChapter = {
            ...chapter,
            chapterName: chapter.chapterName || chapter.name || chapter.title || '未命名章节'
          }
          
          // 递归处理子节点
          if (processedChapter.children && processedChapter.children.length > 0) {
            processedChapter.children = processChapterData(processedChapter.children)
          }
          
          return processedChapter
        })
      }
      
      // 处理API返回的数据，确保所有节点都有chapterName
      options.value = processChapterData(res.data || [])
      
      // 只在没有路由参数时设置默认章节
      if (!query.chapterId && !query.data) {
        chapterId.value = getLast(options.value[0]) || ""
        getMastery()
      }
    }
    loading.value = false
    return res.data || []
  } catch (error) {
    options.value = []
    console.log(error)
    loading.value = false
    return []
  }
}
const getLast = (data: any) => {
  let id = ""
  if(data.children && data.children.length > 0) {
    // 构建路径
    const path = [data]
    const lastId = getLast(data.children[0])
    
    // 如果是第一次调用（没有路径设置），则设置路径
    if (chapterPath.value.length === 0) {
      // 查找完整路径并设置
      findChapterById(options.value, lastId)
    }
    
    return lastId
  } else {
    id = data.id
    
    // 如果是第一次调用（没有路径设置），则设置路径
    if (chapterPath.value.length === 0) {
      chapterPath.value = [data]
    }
    
    return id
  }
}
const setChapterId = (data: any, name: string) => {
  console.log(data,"datadatadatadatadatadatadatadatadatadata")
  // 使用chapterName作为节点名称，如果不存在则回退到name
  const nodeName = data.chapterName || data.name
  pageStatus.value = nodeName != "单元测试"
  chapterId.value = data.chapterId

  // 当用户手动选择章节时，重置标记
  routeHasChapterId.value = false

  // 更新章节路径
  const foundPath = findChapterById(options.value, data.id)
  if (foundPath) {
    chapterPath.value = foundPath
  } else {
    // 如果找不到完整路径，至少设置当前节点
    chapterPath.value = [data]
  }

  // 更新页面数据
  getMastery()

  // 可选：更新URL，保持状态同步（不刷新页面）
  router.replace({
    query: {
      ...route.query,
      chapterId: data.id
    }
  })
}
// 知识点列表
const getMastery = async() => {
  loading2.value = true
  try {
    const res: any = await getpointListApi({
      chapterId: chapterId.value
    })

    if (res.code === 200 && res.data && Array.isArray(res.data)) {
      // 将API返回的知识点数据直接使用，保留原始属性
      lessonData.value = res.data.map((point: any) => ({
        ...point,
        // 确保关键属性存在
        correctRate: point.correctRate || 0,
        studyStatus: point.studyStatus || 0,
        status: point.status || 5, // 默认为未测试
      }));
      
      console.log('知识点数据:', lessonData.value);

      // 如果API返回了掌握度数据，则更新chapterData
      if (res.data[0] && res.data[0].trainCollect) {
        res.data[0].trainCollect.map((item:any) => {
          let num = Number(item.mastery)
          if (item.type == 1) {
            chapterData.percentage1 = num
            chapterData.percentage1i = parseFloat((num).toFixed(2))
          } else if (item.type == 2) {
            chapterData.percentage2 = num
            chapterData.percentage2i = parseFloat((num).toFixed(2))
          } else if (item.type == 3) {
            chapterData.percentage3 = num
            chapterData.percentage3i = parseFloat((num).toFixed(2))
          } else if (item.type == 0) {
            chapterData.percentage0 = num
            chapterData.percentage0i = parseFloat((num).toFixed(2))
          }
        })
      } else {
        // 如果没有掌握度数据，则根据知识点状态计算掌握度
        const totalPoints = lessonData.value.length;
        if (totalPoints > 0) {
          const masteredPoints = lessonData.value.filter(point => point.status === 1).length;
          const masteryPercentage = (masteredPoints / totalPoints) * 100;
          
          chapterData.percentage1 = masteryPercentage;
          chapterData.percentage1i = parseFloat(masteryPercentage.toFixed(2));
        } else {
          resetChapterData();
        }
      }
    } else {
      // 如果API没有返回有效数据，则重置
      lessonData.value = [];
      resetChapterData();
    }
    loading2.value = false;
  } catch (error) {
    console.error('获取知识点数据失败:', error);
    lessonData.value = [];
    resetChapterData();
    loading2.value = false;
  }
}

// 重置章节数据
const resetChapterData = () => {
  chapterData.percentage1 = 0;
  chapterData.percentage1i = 0;
  chapterData.percentage2 = 0;
  chapterData.percentage2i = 0;
  chapterData.percentage3 = 0;
  chapterData.percentage3i = 0;
  chapterData.percentage0 = 0;
  chapterData.percentage0i = 0;
}
const toPractice = (chapterTrainType: number, knowledgeId: string | null = null) => {
  //chapterTrainType 基础巩固1待训练，第二次做传4;综合进阶2,5；难点突破3,6
  router.push({
    path: '/ai_percision/minesweeper/paper_write_switchM',
    query: {
      data: dataEncrypt({
        chapterId: chapterId.value,
        chapterTrainType,
        pageSource: '4',
        contentType: query.contentType,
        knowledgeId: knowledgeId
      })
    }
  })
}
const bookVersionName = computed(() => {
  return subjectObj.value.editionName + learnNow.value.gradeName + (subjectObj.value.termName?subjectObj.value.termName:"")
})
// 获取通关状态
const getCompletionStatus = (knowledge: any) => {
  return knowledge.isCompleted && knowledge.completionLevel
}

// 获取通关徽章图标
const getCompletionBadge = (knowledge: any) => {
  if (knowledge.completionLevel === 'perfect' && knowledge.basicProgress >= 100 && knowledge.advancedProgress >= 100) {
    return new URL(`../../../assets/img/percision/training/组合 <EMAIL>`, import.meta.url).href
  }
  return ''
}

// 判断是否可以显示挑战按钮
const canShowChallengeButton = (lesson: any) => {
  // 检查课程中是否有知识点达到挑战条件（基础训练完成但进阶训练未完成）
  return lesson.knowledgePoints.some((knowledge: any) => 
    knowledge.correctRate >= 70 && knowledge.studyStatus !== 1
  )
}

// 获取挑战按钮文本
const getChallengeButtonText = (lesson: any) => {
  if (!lesson.knowledgePoints || lesson.knowledgePoints.length === 0) {
    return '挑战青铜'
  }
  
  const allCompleted = lesson.knowledgePoints.every((knowledge: any) => 
    knowledge.correctRate >= 100 && knowledge.studyStatus === 1
  )
  
  if (allCompleted) {
    return '完美掌握'
  } else if (canShowChallengeButton(lesson)) {
    return '挑战青铜'
  } else {
    return '再次挑战'
  }
}

// 获取星级图标
const getStarIcon = (filled: boolean) => {
  return filled
    ? new URL(`../../../assets/img/percision/star1.png`, import.meta.url).href
    : new URL(`../../../assets/img/percision/star0.png`, import.meta.url).href
}

// 获取操作按钮图标
const getActionIcon = (percentage: number) => {
  if (percentage > 90) {
    return new URL(`../../../assets/img/percision/strong.png`, import.meta.url).href
  }
  return new URL(`../../../assets/img/percision/pen.png`, import.meta.url).href
}

// 获取成就徽章图标
const getMedalIcon = (type: string) => {
  const medalMap = {
    'basic': 'medal_2.png',
    'advanced': 'medal_3.png',
    'comprehensive': 'medal_1.png'
  }
  return new URL(`../../../assets/img/percision/training/${medalMap[type] || 'medal_1.png'}`, import.meta.url).href
}
// 新增辅助函数
const getHexagonClass = (percentage: number) => {
  if (percentage >= 90) return 'excellent'
  if (percentage >= 70) return 'good'
  if (percentage >= 50) return 'average'
  return 'poor'
}

// 获取掌握度状态类名
const getMasteryClass = (percentage: number) => {
  if (percentage >= 90) return 'excellent'
  if (percentage >= 70) return 'good'
  if (percentage >= 50) return 'average'
  return 'poor'
}

// 获取掌握度文本
const getMasteryText = (percentage: number) => {
  if (percentage >= 90) return '优秀掌握'
  if (percentage >= 70) return '良好掌握'
  if (percentage >= 50) return '一般掌握'
  return '需要加强'
}

// 获取小奖牌图标
const getSmallMedalIcon = (type: number) => {
  const medalMap = {
    1: 'medal_small_1.png',
    2: 'medal_small_2.png',
    3: 'medal_small_3.png',
    4: 'medal_small_4.png',
    5: 'medal_small_5.png',
    6: 'medal_small_6.png',
    7: 'medal_small_7.png',
    8: 'medal_small_8.png'
  }
  return new URL(`../../../assets/img/percision/training/${medalMap[type] || 'medal_small_1.png'}`, import.meta.url).href
}

// 获取完美掌握奖牌
const getPerfectMedalIcon = () => {
  return new URL(`../../../assets/img/percision/training/medal_1.png`, import.meta.url).href
}

// 判断是否显示挑战徽章
const shouldShowChallengeBadge = (percentage1: number, percentage2: number) => {
  return percentage1 >= 70 && percentage2 < 70
}

// 获取挑战徽章图标
const getChallengeBadgeIcon = () => {
  return new URL(`../../../assets/img/percision/training/组合 <EMAIL>`, import.meta.url).href
}

// 获取再次挑战徽章图标
const getRetryBadgeIcon = () => {
  return new URL(`../../../assets/img/percision/training/组合 <EMAIL>`, import.meta.url).href
}

// 获取完美掌握徽章图标
const getPerfectBadgeIcon = () => {
  return new URL(`../../../assets/img/percision/training/组合 <EMAIL>`, import.meta.url).href
}

const onMark = () => {
  router.push({
    path: '/ai_percision/knowledge_hotspot',
    query:{
      bookId:subjectObj.value.bookId,
      subject:query.subject
    }
  })
}
// 获取六边形背景类名
const getHexagonBgClass = (percentage: number, type: string) => {
  let baseClass = type
  if (percentage >= 90) {
    baseClass += ' excellent'
  } else if (percentage >= 70) {
    baseClass += ' good'
  } else if (percentage >= 50) {
    baseClass += ' average'
  } else {
    baseClass += ' poor'
  }
  return baseClass
}

// 获取知识点状态类名
const getKnowledgeStatusClass = (knowledge: any) => {
  // 根据status值确定样式类名
  switch (knowledge.status) {
    case 1: return 'status-mastered'; // 已掌握
    case 2: return 'status-learning'; // 学习中
    case 3: return 'status-failed';   // 未掌握
    case 4: return 'status-testing';  // 测试中
    case 5: return 'status-untested'; // 未测试
    default: return 'status-unknown'; // 未知状态
  }
}

// 获取知识点状态文本
const getKnowledgeStatusText = (knowledge: any) => {
  // 根据status值确定显示文本
  switch (knowledge.status) {
    case 1: return '已掌握';
    case 2: return '学习中';
    case 3: return '未掌握';
    case 4: return '测试中';
    case 5: return '未测试';
    default: return '未知';
  }
}

const onModify = () =>{
  console.log(learnUsers[0].learnId,"learnUserslearnUserslearnUsers")
    router.push({
    path: '/user/user_add',
    query: {
      learnId: learnUsers[0].learnId,
      pageType:'edit'
    }
  })
}

// 知识点详情弹窗相关
const knowledgeDetailVisible = ref(false)
const selectedKnowledge = ref<any>(null)

const showKnowledgeDetail = (knowledge: any) => {
  selectedKnowledge.value = knowledge
  knowledgeDetailVisible.value = true
}

const handleDetailClose = () => {
  knowledgeDetailVisible.value = false
  selectedKnowledge.value = null
}

// 获取知识点难度文本
const getDifficultyText = (point: any) => {
  // 根据知识点的难度值返回对应的文本
  // 这里假设难度值在1-3之间，1为容易，2为中等，3为困难
  const difficultyLevel = point.difficulty || 1;
  
  switch (difficultyLevel) {
    case 1:
      return '容易';
    case 2:
      return '中等';
    case 3:
      return '困难';
    default:
      return '容易';
  }
}

// 计算总题量
const getTotalQuestions = () => {
  if (!selectedLessonForChallenge.value || !selectedLessonForChallenge.value.knowledgePoints) {
    return 6; // 默认值
  }
  
  // 计算所有知识点的题量总和
  return selectedLessonForChallenge.value.knowledgePoints.reduce((total: number, point: any) => {
    return total + (point.total || 3); // 如果没有题量数据，默认为3题
  }, 0);
}

// 计算预估完成时间（分钟）
const getEstimatedTime = () => {
  const totalQuestions = getTotalQuestions();
  // 假设每题平均需要1分钟
  return Math.max(Math.ceil(totalQuestions), 6); // 至少6分钟
}
</script>

<style scoped lang="scss">
.training-page {
  display: flex;
  min-height: calc(100vh - var(--v3-navigationbar-height) - var(--v3-breadcrumb-height));
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
}

.left-sidebar {
  position: relative;
  background: white;
  border-radius: 0 20px 20px 0;
  box-shadow: 2px 0 10px rgba(0, 0, 0, 0.1);

  .sidebar-title {
    position: absolute;
    left: -14px;
    top: 10px;
    width: 179px;
    height: 46px;
    line-height: 46px;
    text-align: center;
    background: linear-gradient(135deg, #00c9a3 0%, #00a085 100%);
    color: white;
    font-size: 20px;
    font-weight: 700;
    border-radius: 0 10px 10px 0;
    z-index: 10;

    .title-decoration {
      position: absolute;
      bottom: -14px;
      left: 0;
      width: 0;
      height: 0;
      border-left: 7px solid transparent;
      border-right: 7px solid #00886e;
      border-top: 7px solid #00886e;
      border-bottom: 7px solid transparent;
    }
  }
}

.main-content {
  flex: 1;
  margin-left: 10px;
  width: calc(100% - 378px);
  height: calc(100vh - var(--v3-navigationbar-height) - var(--v3-breadcrumb-height));
  border-radius: 20px;
  overflow-x: hidden;
  overflow-y: auto;
  box-sizing: border-box;
  background-color: white;
  .content-head{
    position: relative;
    background: url('@/assets/img/percision/training/head-bg.png')center center no-repeat;
    background-size: cover;
    height: 121px;
    padding: 0 20px;
    .head-body{
      position: relative;
      padding: 20px 0 0 0px;
      font-size: 16px;
      font-weight: 600;
      display: flex;
      justify-content: flex-start;
      align-items: center;
      .textbook{
        width: 24px;
        height: 30px;
        margin-right: 6px;
      }
      .head-title{
        margin-right: 10px;
      }
      .head-switch{
        display: flex;
        justify-content: center;
        align-items: center;
        width: 92px;
        height: 27px;
        font-size: 14px;
        font-weight: 600;
        color: #fff;
        background: #00957f;
        border-radius: 14px;
        cursor: pointer;
      }
    }
    .catalogue{
      background: #fff;
      border-radius: 22px;
      margin-top: 20px;
      line-height: 33px;
      padding-left: 16px;
      display: flex;
      span{
        color: rgba(102, 102, 102, 1);
        font-size: 16px;
      }
      img{
        width: 14px;
        height: 9px;
        margin: 12px 12px 12px auto;
      }
    }
    .superficiality{
      position: absolute;
      right: 0;
      top:0;
      width: 120px;
      height: 39px;
      cursor: pointer;
    }
    .head-select{
      position: relative;
      margin: 22px 0 0 20px;
      width: 882px;
      height: 33px;
    }
    .head-select :deep(.el-select__wrapper) {
        border-radius: 15px;
    }
  }
  .tip-content{
    padding: 0 16px;
    display: flex;
    justify-content: flex-start;
    height: 44px;
    align-items: center;
    .tip-avatar{
      width: 28px;
      height: 28px;
      margin-right: 10px;
    }
    .tip-text{
      color: #3294DB;
      font-size: 12px;
    }
  }
  .content-tip{
    position: relative;
    margin: 10px auto;
    width: 882px;
    height: 44px;
    border-radius: 4px;
    background: rgba(236, 247, 255, 1);
    .tip-bg{
      position: absolute;
      top: 0;
      left: 0;
      width: 882px;
      height: 44px;
    }
  }
}

.knowledge-training-list {
  padding:0 20px;
  .lesson-section {
    margin-bottom: 30px;
    
    background: #FAFBFD;
    // border-radius: 8px;
    // overflow: hidden;

    .lesson-tag {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 60px;
      height: 24px;
      text-align: center;
      border: 1px solid #5A85EC;
      background: rgba(238, 243, 253, 1);
      color: rgba(90, 133, 236, 1);
      font-size: 12px;
      font-weight: 400;
      // border-radius: 0 0 8px 0;
    }
  }
}

.knowledge-info {
 
  .knowledge-box {
    display: flex;
    flex-direction: column;
    gap: 10px;
  }
}

.knowledge-item {
  display: flex;
  align-items: center;
  margin: 0 20px;
  padding: 10px 0 30px 0;
  position: relative;
  transition: all 0.2s ease;
  border-bottom: 1px dashed rgba(234, 234, 234, 1);
  
  &:last-child {
    border-bottom: none;
  }

  .knowledge-name {
    width: 280px;
    padding-right: 20px;
    border-right: 1px solid #EAEAEA;
    margin-right: 16px;

    .knowledge-title {
      font-size: 14px;
      font-weight: 400;
      color: #333;
      line-height: 1.4;
      display: -webkit-box;
      -webkit-line-clamp: 2; /* 限制为两行 */
      -webkit-box-orient: vertical;
      overflow: hidden;
      text-overflow: ellipsis;
      padding-right: 60px; /* 为状态标签留出空间 */
    }
  }

  .progress-area {
    display: flex;
    align-items: center;
    margin-right: 20px;

    .hexagon-group {
      display: flex;
      gap: 20px;
      align-items: center;
    }
  }

  .action-area {
    display: flex;
    gap: 8px;
  }
}

.hexagon-wrapper {
  display: flex;
  flex-direction: column;
  align-items: center;

  .hexagon-bg {
    width: 50px;
    height: 50px;
    position: relative;
    background: url('@/assets/img/percision/training/medal_small_1.png')center no-repeat;
    background-size: cover;
    &.basic {
      // 基础训练 - 绿色系
      &.excellent {
        background: url('@/assets/img/percision/training/medal_small_2.png')center no-repeat;
      }
      &.good {
        background: url('@/assets/img/percision/training/medal_small_3.png')center no-repeat;

      }
      &.average {
        background: url('@/assets/img/percision/training/medal_small_4.png')center no-repeat;

      }
      &.poor {
        background: url('@/assets/img/percision/training/medal_small_5.png')center no-repeat;

      }
    }

    &.advanced {
      // 进阶训练 - 蓝色系
      &.excellent {
        background: url('@/assets/img/percision/training/medal_small_6.png')center no-repeat;

      }
      &.good {
        background: url('@/assets/img/percision/training/medal_small_7.png')center no-repeat;

      }
      &.average {
        background: url('@/assets/img/percision/training/medal_small_8.png')center no-repeat;
      }
      &.poor {
        background: url('@/assets/img/percision/training/medal_small_6.png')center no-repeat;

      }
    }

    .hexagon-content {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      text-align: center;
      color: white;

      .percentage-text {
        font-size: 10px;
        font-weight: 600;
        margin-bottom: 2px;
        font-size: 12px;
        padding: 3px;
        position: absolute;
        bottom: -50px;
        color: rgba(0, 156, 127, 1);
        left: 50%;
        transform: translateX(-50%);
        background: rgba(229, 249, 246, 1);
      }

      .medal-crown {
        position: absolute;
        top: -12px;
        left: 50%;
        transform: translateX(-50%);
        width: 16px;
        height: 16px;
      }
    }
  }
}

// 按钮样式
.action-btn {
  display: flex;
  align-items: center;
  height: 19px;
  font-size: 14px;
  align-items: center;
  justify-content: space-between;
  color: #999999;
  cursor: pointer;
  .action-icon {
    width: 16px;
    height: 16px;
    margin-right: 6px;
    &.study-icon {
      background: url('@/assets/img/percision/pen.png') no-repeat center;
      background-size: contain;
      margin-right: 6px;
    }

    &.practice-icon {
      background: url('@/assets/img/percision/strong.png') no-repeat center;
      background-size: contain;
    }
  }

  &.study {
    background: #f8f9fa;
    padding-left: 6px;
    // border: 1px solid #e9ecef;
    text-decoration: underline;
    &:hover {
      // background: #e9ecef;
      color: #495057;
    }
  }

  &.practice {
    background: #f8f9fa;
    padding-left: 6px;
    // border: 1px solid #e9ecef;
    text-decoration: underline;
    &:hover {
      // background: #e9ecef;
      color: #495057;
    }
  }
}

// 成就徽章样式
.achievement-badge {
  position: absolute;
  top: 8px;
  right: 8px;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 10px;
  font-weight: 500;
  color: white;
  z-index: 2;

  &.challenge {
    background: linear-gradient(135deg, #ff6b35 0%, #f7931e 100%);
  }

  &.retry {
    background: linear-gradient(135deg, #ff6b35 0%, #f7931e 100%);
  }

  &.perfect {
    background: transparent;
    padding: 0;

    .perfect-icon {
      width: 60px;
      height: auto;
      filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.2));
    }
  }
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.lesson-section {
  animation: fadeInUp 0.5s ease-out;

  &:nth-child(1) { animation-delay: 0.1s; }
  &:nth-child(2) { animation-delay: 0.2s; }
  &:nth-child(3) { animation-delay: 0.3s; }
}

.knowledge-status {
  position: absolute;
  top: 10px;
  right: 10px;
  padding: 2px 8px;
  border-radius: 10px;
  font-size: 12px;
  font-weight: 500;
  color: white;
}

.status-mastered {
  background: linear-gradient(166.7deg, #08d8b8 0%, #00b392 100%);
}

.status-learning {
  background: linear-gradient(150.8deg, #5a85ec 0%, #3a65cc 100%);
}

.status-failed {
  background: linear-gradient(150.8deg, #f07f4c 0%, #c95656 100%);
}

.status-testing {
  background: linear-gradient(150.8deg, #f6d22b 0%, #f29500 100%);
}

.status-untested {
  background: #bbbbbb;
}

.status-unknown {
  background: #999999;
}

.knowledge-detail-content {
  padding: 20px;
  max-height: 60vh;
  overflow-y: auto;
  line-height: 1.6;
  font-size: 14px;
  color: #333;
  
  :deep(table) {
    border-collapse: collapse;
    width: 100%;
    margin: 10px 0;
    
    td, th {
      border: 1px solid #ddd;
      padding: 8px;
      text-align: center;
    }
    
    tr:nth-child(even) {
      background-color: #f2f2f2;
    }
  }
  
  :deep(br) {
    margin-bottom: 5px;
  }
}

.knowledge-name {
  cursor: pointer;
  
  &:hover .knowledge-title {
    color: #5a85ec;
    text-decoration: underline;
  }
}

.lesson-container {
  border: 1px solid #EAEAEA;
  overflow: hidden;
  background: #FFFFFF;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.05);
  position: relative;
}

.lesson-title {
  text-align: center;
  font-size: 12px;
  color: #5A85EC;
  width: 60px;
  line-height: 24px;
  border: 1px solid #5A85EC;
  background: rgba(238, 243, 253, 1);
}

.lesson-content {
  padding: 10px 0 0 0;
}

.empty-state {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
  background: #F8F9FA;
  border-radius: 8px;
  margin: 20px 0;
}

.empty-message {
  font-size: 16px;
  color: #999;
}

.challenge-btn {
  width: 112px;
  height: 48px;
  background: url('@/assets/img/percision/training/challenge_btn_bg.png')center no-repeat;
  background-size: 100%;
  font-size: 16px;
  font-weight: 700;
  color: #fff;
  cursor: pointer;
  display: flex;
  justify-content: center;
  align-items: center;
  position: absolute;
  top: 5%;
  transform: translateY(-50%);
  right: 20px;
  bottom: 20px;
  opacity: 1;
  transition: opacity 0.3s ease;
  
}
</style>
<style lang="scss" scoped>
@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.elevate-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.7);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 9999;
}

.elevate-ct {
  width: 812px;
  height: 516px;
  border-radius: 20px;
  position: relative;
  background:  url("@/assets/img/percision/training/ytzk.png") no-repeat;
  background-size: 100%;
  color: #fff;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  text-align: center;
}
.top-title{
  width: 160px;
  margin: 0 auto;
  padding-top: 86px;
  font-size: 16px;
  text-align: center;
  font-weight: bold;
  color: #fff; /* 内部为白色文字 */
  text-shadow: 
    1px 1px 0 rgba(217, 78, 50, 1),  
    -1px -1px 0 rgba(217, 78, 50, 1),  
    1px -1px 0 rgba(217, 78, 50, 1),  
    -1px 1px 0 rgba(217, 78, 50, 1); /* 外部为红色描边 */
  }
  .block-ct{
    width: 652px;
    height: 146px;background: rgba(255, 255, 255, 1);margin: 0 auto;margin-top: 20px;box-shadow: 0 0 3px 3px rgba(208, 192, 169, 1);border-radius: 14px;
    padding: 30px 50px 20px 30px;
    text-align: center;
    .book-list{
    padding: 0 50px 0 30px;
    display: flex;
    margin-bottom: 20px;
    img{
      width: 24px;
      height: 24px;
      margin-right: 8px;
    }
    .book-name{
      color: rgba(50, 58, 87, 1);
      font-size: 16px;
      width: 300px;
      text-align: left;
      white-space: nowrap;       /* 防止文字换行 */
      overflow: hidden;          /* 隐藏超出部分 */
      text-overflow: ellipsis; 
    }
    .book-tl{
      color: rgba(50, 58, 87, 1);
      font-size: 16px;
      padding-left: 46px;
      span{
        font-size: 20px;
        font-weight: 700;
        color: rgba(255, 151, 31, 1);
        padding-left: 10px;
      }
    }
  }
  .prompt{
    text-align: center;
    display: flex;
    color: rgba(42, 43, 42, 1);
    font-size: 16px;
    width: 260px;
    margin: 0 auto;
    display: flex;
    align-items: center;
    justify-content: center;
    display: inline-block;
    padding-top: 20px;
    span{
      font-size: 20px;
      color: rgba(255, 151, 31, 1);
    }
  }
  }
  .challenge-fq{
  display: flex;
  color: rgba(50, 58, 87, 1);
  justify-content: center;
  align-items: center;
  margin: 26px auto 0 auto;
  font-size: 16px;
  img{
    width: 66px;
    height: 30px;
    margin: 0 5px;
  }
}
.book-challenge{
    width: 112px;
    height: 46px;
    background: url('@/assets/img/percision/training/challenge_btn_bg.png')center no-repeat;
    background-size: 100%;
    font-weight: 700;
    color: #fff;
    cursor: pointer;
    font-size: 16px;
    line-height: 46px;
    text-align: center;
    margin: 50px auto 0 auto;
}
.click-bt{
 display: flex;
 cursor: pointer;
}

.close-btn {
  position: absolute;
  top: -10px;
  right: -20px;
  margin-top: 20px;
  border-radius: 50%;
  // background-color: rgba(0, 0, 0, 0.1);
  color: #333;
  font-size: 24px;
  display: flex;
  justify-content: center;
  align-items: center;
  cursor: pointer;
  transition: all 0.3s ease;
  img{
    width: 54px;
    height: 54px;
  }
}

.close-btn:hover {
  // background-color: rgba(0, 0, 0, 0.2);
  transform: scale(1.1);
}
</style>
